'use client'

import * as React from "react"
import { useState, useMemo, useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { createAnchorItems, DocumentContent, extractHeadings } from '../../components/document'
import DocumentSidebar from "../../components/document/DocumentSidebar"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Menu } from "lucide-react"
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import Link from "next/link"

interface RepositoryViewProps {
  owner: string;
  name: string;
  document: any;
}

export function RepositoryView({ owner, name, document }: RepositoryViewProps) {
  const [headings, setHeadings] = useState<{key: string, title: string, level: number, id: string}[]>([]);
  
  const anchorItems = useMemo(() => {
    return createAnchorItems(headings);
  }, [headings]);
  
  useEffect(() => {
    // 提取标题作为目录
    if (document?.content) {
      const extractedHeadings = extractHeadings(document.content);
      setHeadings(extractedHeadings);
    }
  }, [document]);

  return (
    <SidebarProvider>
      <DocumentSidebar 
        anchorItems={anchorItems} 
        repositoryName={name}
        organizationName={owner}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger>
              <Menu className="h-4 w-4" />
            </SidebarTrigger>
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href="/">首页</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${owner}`}>{owner}</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{name}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex flex-1 flex-col gap-4 p-4">
          <Card className="flex-1 p-6">
            <DocumentContent
              document={document}
              owner={owner}
              name={name}
              token={null}
            />
          </Card>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
} 