'use client'

import * as React from "react"
import { useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  SidebarProvider, 
  SidebarInset, 
  SidebarTrigger 
} from "@/components/ui/sidebar"
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import DocumentSidebar from "../../../components/document/DocumentSidebar"
import { AlertTriangle, Menu } from "lucide-react"
import Link from "next/link"

// 导入封装好的组件
import {
  DocumentContent,
  LoadingErrorState,
  DocumentStyles,
  initializeMermaid,
  SourceFiles
} from '../../../components/document'

// 导入类型
import { DocumentPageClientProps } from './types'

export default function DocumentPageClient({
  document,
  error,
  headings,
  anchorItems,
  owner,
  name,
  path,
  branch
}: DocumentPageClientProps) {

  // 初始化mermaid配置
  useEffect(() => {
    initializeMermaid(false) // 使用浅色主题
  }, [])

  // 错误状态
  const ErrorState = () => (
    <div className="flex items-center justify-center min-h-[400px]">
      <Alert className="max-w-md">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="mt-2">
          <div className="space-y-2">
            <p className="font-medium">文档加载失败</p>
            <p className="text-sm text-muted-foreground">{error}</p>
            <Button asChild variant="outline" size="sm">
              <Link href={`https://github.com/${owner}/${name}`} target="_blank">
                在GitHub上查看
              </Link>
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  )

  // 源文件组件
  const SourceFilesSection = () => (
    document?.fileSource && document.fileSource.length > 0 && (
      <Card className="p-4 mb-4">
        <div className="flex items-center gap-2 mb-3">
          <Badge variant="secondary">源文件</Badge>
          <span className="text-sm text-muted-foreground">
            {document.fileSource.length} 个文件
          </span>
        </div>
        <SourceFiles
          fileSource={document.fileSource}
          owner={owner}
          branch={document.branch}
          git={document.address}
          name={name}
          token={null}
        />
      </Card>
    )
  )

  return (
    <SidebarProvider>
      <DocumentSidebar 
        anchorItems={anchorItems} 
        repositoryName={name}
        organizationName={owner}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger>
              <Menu className="h-4 w-4" />
            </SidebarTrigger>
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">首页</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/${owner}`}>{owner}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/${owner}/${name}`}>{name}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{path}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        
        <main className="flex flex-1 flex-col gap-4 p-4">
          {error ? (
            <ErrorState />
          ) : (
            <>
              <SourceFilesSection />
              <Card className="flex-1 p-6">
                <DocumentContent
                  document={document}
                  owner={owner}
                  name={name}
                  token={null}
                />
              </Card>
            </>
          )}
        </main>
      </SidebarInset>
      
      <DocumentStyles token={null} />
    </SidebarProvider>
  )
} 