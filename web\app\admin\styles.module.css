/* 管理界面全局样式 - 扁平化设计 */
.adminLayout {
  height: 100vh;
  background: #ffffff;
  color: #000000;
  font-family: system-ui, -apple-system, 'Segoe UI', sans-serif;
  overflow: hidden; /* 防止整体滚动 */
}

/* 8px 网格系统变量 */
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 色彩系统 */
  --color-primary: #000000;
  --color-secondary: #8c8c8c;
  --color-text: #000000;
  --color-text-secondary: #8c8c8c;
  --color-background: #ffffff;
  --color-border: #e8e8e8;
  --color-accent: #1677ff;
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #ff4d4f;
  
  /* 字体 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
  
  /* 边框半径 */
  --border-radius: 4px;
  --border-radius-lg: 8px;
}

/* 布局组件 */
.sidebarContainer {
  background: var(--color-background);
  border-right: 1px solid var(--color-border);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  /* 移除所有阴影效果 */
}

.sidebarLogo {
  background: var(--color-primary);
  border-radius: var(--border-radius);
  color: var(--color-background);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 101;
}

/* 导航项样式 */
.navItem {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--color-text-secondary);
  background: transparent;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: 400;
  border: none;
}

.navItem:hover {
  background: #f5f5f5;
  color: var(--color-text);
  text-decoration: none;
}

.navItemActive {
  background: #e6f4ff;
  color: var(--color-accent);
  font-weight: 500;
}

.navItemIcon {
  font-size: var(--font-size-lg);
  min-width: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navItemLabel {
  margin-left: var(--spacing-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 头部样式 */
.headerContainer {
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 99;
}

.menuToggle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  border: none;
  background: transparent;
}

.menuToggle:hover {
  background: #f5f5f5;
  color: var(--color-text);
}

/* 用户信息区域 */
.userInfo {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.userInfo:hover {
  background: #f5f5f5;
}

.userAvatar {
  background: var(--color-primary);
  margin-right: var(--spacing-sm);
}

.userName {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* 主内容区域 */
.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
  height: 100vh;
  overflow: hidden; /* 防止布局容器滚动 */
}

.contentContainer {
  flex: 1;
  padding: var(--spacing-lg);
  overflow: auto; /* 只有内容区域滚动 */
  height: calc(100vh - 112px); /* 减去头部和边距 */
}

.contentWrapper {
  background: var(--color-background);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
  padding: var(--spacing-lg);
  min-height: 100%;
}

/* 卡片组件 */
.card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
}

/* 统计卡片 */
.statCard {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  transition: border-color 0.2s ease;
}

.statCard:hover {
  border-color: var(--color-accent);
}

.statCardIcon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--spacing-lg);
}

/* 按钮样式 */
.button {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
}

.buttonPrimary {
  background: var(--color-accent);
  border-color: var(--color-accent);
  color: var(--color-background);
}

.buttonPrimary:hover {
  background: #4096ff;
  border-color: #4096ff;
}

.buttonDanger {
  background: var(--color-error);
  border-color: var(--color-error);
  color: var(--color-background);
}

.buttonDanger:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.tableHeader {
  background: #fafafa;
  font-weight: 600;
  color: var(--color-text);
}

.tableCell {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  font-size: var(--font-size-sm);
}

.tableRow:hover {
  background: #fafafa;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border: none;
}

.tagSuccess {
  background: #f6ffed;
  color: var(--color-success);
}

.tagWarning {
  background: #fff7e6;
  color: var(--color-warning);
}

.tagError {
  background: #fff2f0;
  color: var(--color-error);
}

.tagInfo {
  background: #e6f4ff;
  color: var(--color-accent);
}

/* 表单元素 */
.input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: var(--color-accent);
  outline: none;
}

.textarea {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  resize: vertical;
  min-height: 80px;
}

.select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebarContainer {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebarContainer.sidebarOpen {
    transform: translateX(0);
  }
  
  .mainContent {
    margin-left: 0 !important;
  }
  
  .contentContainer {
    padding: var(--spacing-md);
  }
  
  .contentWrapper {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
  }
}

/* 滚动条样式 - 只应用于侧边栏菜单和内容区域 */
.ant-layout-sider .ant-menu::-webkit-scrollbar,
.ant-layout-content::-webkit-scrollbar {
  width: 8px;
}

.ant-layout-sider .ant-menu::-webkit-scrollbar-track,
.ant-layout-content::-webkit-scrollbar-track {
  background: var(--color-background);
  border-radius: 4px;
}

.ant-layout-sider .ant-menu::-webkit-scrollbar-thumb,
.ant-layout-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  border: 2px solid var(--color-background);
}

.ant-layout-sider .ant-menu::-webkit-scrollbar-thumb:hover,
.ant-layout-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* 确保侧边栏滚动条不影响布局 */
.ant-layout-sider .ant-menu {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-background);
}

/* 内容区域滚动条样式 */
.ant-layout-content {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-background);
}

/* 加载状态样式 */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-md);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* 工具类 */
.textPrimary {
  color: var(--color-text);
}

.textSecondary {
  color: var(--color-text-secondary);
}

.textAccent {
  color: var(--color-accent);
}

.textSuccess {
  color: var(--color-success);
}

.textWarning {
  color: var(--color-warning);
}

.textError {
  color: var(--color-error);
}

.spacingXs {
  margin: var(--spacing-xs);
}

.spacingSm {
  margin: var(--spacing-sm);
}

.spacingMd {
  margin: var(--spacing-md);
}

.spacingLg {
  margin: var(--spacing-lg);
}

.spacingXl {
  margin: var(--spacing-xl);
}

.paddingXs {
  padding: var(--spacing-xs);
}

.paddingSm {
  padding: var(--spacing-sm);
}

.paddingMd {
  padding: var(--spacing-md);
}

.paddingLg {
  padding: var(--spacing-lg);
}

.paddingXl {
  padding: var(--spacing-xl);
}

/* 移除所有阴影和渐变 */
* {
  box-shadow: none !important;
  background-image: none !important;
  text-shadow: none !important;
}

/* 确保按钮没有阴影 */
button,
.ant-btn {
  box-shadow: none !important;
}

/* 确保卡片没有阴影 */
.ant-card {
  box-shadow: none !important;
}

/* 确保模态框没有阴影 */
.ant-modal {
  box-shadow: none !important;
}

/* 确保下拉菜单没有阴影 */
.ant-dropdown {
  box-shadow: none !important;
}
