.document-sidebar {
  width: 100%;
  font-size: 13px;
  background-color: #fafafa;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  margin: 0 0 16px 0;
  position: relative;
}

.sidebar-navigation {
  width: 100%;
  padding: 16px;
}

.sidebar-list {
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
  position: relative;
}

.sidebar-item {
  margin-bottom: 8px;
  position: relative;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item:last-child {
  margin-bottom: 0;
}

.dot-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #e8e8e8;
  display: inline-block;
  margin-right: 8px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.dot-indicator.small {
  width: 4px;
  height: 4px;
  margin-right: 6px;
}

.sidebar-item.active .dot-indicator {
  background-color: #1677ff;
  transform: scale(1.2);
}

.sidebar-subitem.active .dot-indicator {
  background-color: #1677ff;
  transform: scale(1.2);
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  color: #454545;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.sidebar-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: #1677ff;
  opacity: 0;
  transition: width 0.2s ease, opacity 0.2s ease;
}

.sidebar-item.active > .sidebar-link {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.08);
  font-weight: 600;
}

.sidebar-item.active > .sidebar-link::before {
  width: 3px;
  opacity: 1;
}

.sidebar-link:hover {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.05);
  transform: translateX(2px);
  text-decoration: none;
}

.sidebar-link:hover .dot-indicator {
  background-color: #1677ff;
  opacity: 0.7;
}

.sidebar-sublist {
  list-style: none;
  margin: 6px 0 0 0;
  padding-left: 16px;
  position: relative;
}

.sidebar-sublist::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.08);
}

.sidebar-subitem {
  margin-bottom: 4px;
  position: relative;
}

.sidebar-subitem:last-child {
  margin-bottom: 0;
}

.sidebar-sublink {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  font-size: 12px;
  line-height: 1.5;
  cursor: pointer;
  color: #666;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.sidebar-sublink::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: #1677ff;
  opacity: 0;
  transition: width 0.2s ease, opacity 0.2s ease;
}

.sidebar-subitem.active > .sidebar-sublink {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.08);
  font-weight: 600;
}

.sidebar-subitem.active > .sidebar-sublink::before {
  width: 2px;
  opacity: 1;
}

.sidebar-sublink:hover {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.05);
  transform: translateX(2px);
  text-decoration: none;
}

.sidebar-sublink:hover .dot-indicator {
  background-color: #1677ff;
  opacity: 0.7;
}

.sidebar-empty {
  padding: 20px;
  color: #999;
  text-align: center;
  font-size: 13px;
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 6px;
} 