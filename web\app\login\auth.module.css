.authContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.authWrapper {
  width: 100%;
  max-width: 420px;
  animation: fadeIn 0.5s ease-out;
}

.authCard {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.authHeader {
  text-align: center;
  margin-bottom: 24px;
}

.authTitle {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1677ff;
}

.authSubtitle {
  color: #8c8c8c;
  font-size: 14px;
}

.authForm {
  margin-top: 16px;
}

.rememberForgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgotLink {
  color: #1677ff;
  font-size: 14px;
}

.loginButton {
  height: 44px;
  font-size: 16px;
  border-radius: 6px;
}

.registerLink {
  text-align: center;
  margin: 16px 0;
  color: #8c8c8c;
}

.socialLogin {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

.socialButton {
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
}

.socialButton:hover {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
}

.siteFormItemIcon {
  color: #bfbfbf;
}

/* 注册页面特定样式 */
.agreement {
  margin-bottom: 24px;
}

.agreementText {
  color: #8c8c8c;
  font-size: 14px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 