body{
  margin: 0;
}
/* 页面加载器 */
.pageLoader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loaderContent {
  text-align: center;
}

.loaderIcon {
  font-size: 32px;
  margin-bottom: 12px;
}

.loaderText {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6px;
}

.loaderSubtext {
  font-size: 13px;
  color: #666666;
  margin-bottom: 16px;
}

.loaderSpinner {
  display: flex;
  justify-content: center;
  gap: 3px;
}

.spinnerRing {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999999;
  animation: spinnerBounce 1.2s ease-in-out infinite both;
}

.spinnerRing:nth-child(1) { animation-delay: -0.24s; }
.spinnerRing:nth-child(2) { animation-delay: -0.12s; }
.spinnerRing:nth-child(3) { animation-delay: 0s; }

@keyframes spinnerBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 主容器 */
.container {
  display: grid;
  grid-template-columns: 1fr 280px;
  height: 100vh;
  background: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* 聊天区域 */
.chatSection {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
}

/* 头部 */
.header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333333;
  font-weight: 500;
  font-size: 16px;
}

.logoIcon {
  font-size: 18px;
  margin-right: 6px;
}

.logoText {
  color: #333333;
}

/* 消息容器 */
.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
}

.messagesContainer::-webkit-scrollbar {
  width: 4px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: transparent;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 2px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #999999;
}

.emptyIcon {
  font-size: 32px;
  margin-bottom: 12px;
}

.emptyText {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #666666;
}

.emptySubtext {
  font-size: 13px;
  color: #999999;
}

.messageWrapper {
  margin-bottom: 16px;
}

.userMessage {
  display: flex;
  justify-content: flex-end;
}

.aiMessage {
  display: flex;
  justify-content: flex-start;
}

.messageContent {
  max-width: 75%;
}

.userText {
  background: transparent;
  color: #333333;
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.aiText {
  background: transparent;
  color: #333333;
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.loadingDots {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 8px 0;
}

.loadingDots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #cccccc;
  animation: bounce 1.2s ease-in-out infinite both;
}

.loadingDots span:nth-child(1) { animation-delay: -0.24s; }
.loadingDots span:nth-child(2) { animation-delay: -0.12s; }
.loadingDots span:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 输入区域 */
.inputSection {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #ffffff;
}

.inputContainer {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  max-width: 100%;
  background: #f8f8f8;
  border-radius: 18px;
  padding: 6px;
}

.messageInput {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  min-height: 18px;
  max-height: 100px;
}

.messageInput::placeholder {
  color: #aaaaaa;
}

.sendButton {
  width: 32px;
  height: 32px;
  border: none;
  background: #007bff;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.sendButton:hover:not(:disabled) {
  background: #0056b3;
}

.sendButton:disabled {
  background: #dddddd;
  cursor: not-allowed;
}

/* 侧边栏 */
.sidebar {
  background: #ffffff;
  border-left: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebarHeader {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebarTitle {
  font-weight: 500;
  color: #666666;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.sidebarIcon {
  margin-right: 6px;
}

.fileListView {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fileList {
  flex: 1;
  overflow-y: auto;
  padding: 6px;
}

.fileList::-webkit-scrollbar {
  width: 4px;
}

.fileList::-webkit-scrollbar-track {
  background: transparent;
}

.fileList::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 2px;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 2px;
}

.fileItem:hover {
  background: #f8f8f8;
}

.fileIcon {
  font-size: 14px;
  margin-right: 8px;
  flex-shrink: 0;
}

.fileInfo {
  flex: 1;
  min-width: 0;
}

.fileTitle {
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filePath {
  font-size: 11px;
  color: #999999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 文件内容视图 */
.fileContentView {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fileHeader {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backButton {
  background: none;
  border: 1px solid #e0e0e0;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #666666;
}

.backButton:hover {
  background: #f8f8f8;
}

.fileName {
  flex: 1;
  font-weight: 500;
  color: #333333;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.copyButton {
  background: none;
  border: 1px solid #e0e0e0;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.copyButton:hover {
  background: #f8f8f8;
}

.fileContentContainer {
  flex: 1;
  overflow: auto;
}

.fileContent {
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: #fafafa;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.fileLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #999999;
}

.emptyFiles {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  text-align: center;
  color: #999999;
}

.emptyFilesIcon {
  font-size: 24px;
  margin-bottom: 8px;
}

.emptyFilesText {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 3px;
  color: #666666;
}

.emptyFilesSubtext {
  font-size: 11px;
  color: #999999;
}

/* 骨架屏 */
.fileSkeleton {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 2px;
}

.skeletonIcon {
  width: 14px;
  height: 14px;
  background: #f0f0f0;
  border-radius: 2px;
  margin-right: 8px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeletonContent {
  flex: 1;
}

.skeletonTitle {
  height: 12px;
  background: #f0f0f0;
  border-radius: 2px;
  margin-bottom: 4px;
  width: 60%;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeletonPath {
  height: 10px;
  background: #f0f0f0;
  border-radius: 2px;
  width: 80%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.loadingSpinner {
  width: 14px;
  height: 14px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #999999;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .container {
    grid-template-columns: 1fr 260px;
  }
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    display: none;
  }
  
  .messageContent {
    max-width: 85%;
  }
  
  .inputSection {
    padding: 10px 12px;
  }
  
  .header {
    padding: 10px 12px;
  }
  
  .messagesContainer {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .messageContent {
    max-width: 90%;
  }
  
  .userText, .aiText {
    font-size: 13px;
    padding: 8px 12px;
  }
} 