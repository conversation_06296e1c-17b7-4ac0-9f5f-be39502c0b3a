'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronRight } from "lucide-react"
import { useEffect, useState } from "react"

interface AnchorItem {
  key: string;
  title: string;
  href?: string;
  children?: AnchorItem[];
}

interface DocumentSidebarProps {
  anchorItems: AnchorItem[];
  repositoryName?: string;
  organizationName?: string;
}

export function DocumentSidebar({
  anchorItems,
  repositoryName,
  organizationName,
}: DocumentSidebarProps) {
  const [activeAnchor, setActiveAnchor] = useState<string>('');

  // 处理锚点点击事件
  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string, title: string) => {
    e.preventDefault();
    
    if (href) {
      // 首先尝试通过ID查找元素（需要处理无效选择器）
      let element: Element | null = null;
      
      try {
        element = document.querySelector(href);
      } catch (error) {
        console.warn('无效的CSS选择器:', href);
      }
      
      // 如果通过ID找不到，尝试通过标题文本查找
      if (!element && title) {
        // 查找所有标题元素
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        for (const heading of headings) {
          if (heading.textContent?.trim() === title.trim()) {
            element = heading;
            break;
          }
        }
      }
      
      if (element) {
        // 平滑滚动到目标元素
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
        
        // 更新活动锚点
        setActiveAnchor(href);
        
        // 更新URL hash（可选）
        window.history.pushState(null, '', href);
        
        // 添加临时高亮效果
        const htmlElement = element as HTMLElement;
        htmlElement.style.transition = 'background-color 0.3s ease';
        htmlElement.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        setTimeout(() => {
          htmlElement.style.backgroundColor = '';
        }, 2000);
      }
    }
  };

  // 监听滚动事件，自动更新活动锚点
  useEffect(() => {
    const findElementByHrefOrTitle = (href: string, title: string) => {
      let element: Element | null = null;
      
      try {
        element = document.querySelector(href);
      } catch (error) {
        console.warn('无效的CSS选择器:', href);
      }
      
      if (!element && title) {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        for (const heading of headings) {
          if (heading.textContent?.trim() === title.trim()) {
            element = heading;
            break;
          }
        }
      }
      
      return element;
    };

    const handleScroll = () => {
      // 查找当前可见的章节
      anchorItems.forEach(item => {
        if (item.href) {
          const element = findElementByHrefOrTitle(item.href, item.title);
          if (element) {
            const { top } = element.getBoundingClientRect();
            if (top <= 100) {
              setActiveAnchor(item.href);
            }
          }
        }

        // 检查子项
        if (item.children) {
          item.children.forEach(child => {
            if (child.href) {
              const element = findElementByHrefOrTitle(child.href, child.title);
              if (element) {
                const { top } = element.getBoundingClientRect();
                if (top <= 100) {
                  setActiveAnchor(child.href);
                }
              }
            }
          });
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    // 初始检查当前 hash
    if (window.location.hash) {
      setActiveAnchor(window.location.hash);
    }
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [anchorItems]);

  return (
    <div className="hidden md:block">
      <div className="fixed inset-y-0 left-0 z-30 w-72 border-r bg-background">
        <div className="flex h-16 items-center border-b px-4">
          {organizationName && repositoryName && (
            <div className="flex items-center gap-2 text-sm font-medium">
              <span className="text-muted-foreground">{organizationName}</span>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
              <span>{repositoryName}</span>
            </div>
          )}
        </div>
        <div className="flex-1 overflow-auto p-4">
          {anchorItems && anchorItems.length > 0 ? (
            <div className="space-y-1">
              {anchorItems.map((item) => (
                <div key={item.key} className="space-y-1">
                  <a
                    href={item.href}
                    onClick={(e) => handleAnchorClick(e, item.href || '', item.title)}
                    className={cn(
                      "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                      activeAnchor === item.href
                        ? "bg-accent text-accent-foreground"
                        : "hover:bg-accent/50 hover:text-accent-foreground"
                    )}
                  >
                    {item.title}
                  </a>
                  
                  {item.children && item.children.length > 0 && (
                    <div className="ml-4 space-y-1">
                      {item.children.map((child) => (
                        <a
                          key={child.key}
                          href={child.href}
                          onClick={(e) => handleAnchorClick(e, child.href || '', child.title)}
                          className={cn(
                            "flex items-center rounded-md px-3 py-1.5 text-sm transition-colors",
                            activeAnchor === child.href
                              ? "bg-accent/70 text-accent-foreground"
                              : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                          )}
                        >
                          {child.title}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-4 text-center text-sm text-muted-foreground">
              暂无目录
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 