{"homepage": "https://github.com/AIDotNet/OpenDeepWiki", "scripts": {"dev": "next dev", "build": "next build && npm run build:widget", "build:widget": "node widget-build.js", "dev:widget": "node widget-build.js --watch", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@lobehub/ui": "^2.7.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/vite": "^4.1.11", "@types/d3": "^7.4.3", "@types/mdx": "^2.0.13", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-md-editor": "^4.0.6", "accept-language": "^3.0.20", "antd": "^5.24.8", "antd-style": "^3.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "fumadocs-core": "^15.6.1", "fumadocs-mdx": "^11.6.10", "fumadocs-ui": "^15.6.1", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-resources-to-backend": "^1.2.1", "immer": "^10.1.1", "lucide-react": "^0.525.0", "marked": "^15.0.11", "md-editor-rt": "^5.6.0", "mermaid": "^11.6.0", "mind-elixir": "^4.6.1", "next": "^15.3.1", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-math": "^6.0.0", "remark-rehype": "^11.1.2", "remark-toc": "^9.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/node": "22.15.2", "@types/react": "19.1.2", "autoprefixer": "^10.4.21", "esbuild": "^0.20.2", "postcss": "^8.5.3", "serve-handler": "^6.1.6", "tailwindcss": "^4.1.11", "typescript": "5.8.3"}}