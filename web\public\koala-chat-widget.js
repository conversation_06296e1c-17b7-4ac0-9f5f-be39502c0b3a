(()=>{function d(n){if(n.apiUrl)return n.apiUrl;let t=document.querySelector('script[src*="koala-chat-widget.js"]');if(t){let i=t.getAttribute("src");if(i)try{return new URL(i,window.location.href).origin}catch{console.warn("Unable to parse script source URL:",i)}}return window.location.origin}function l(n){return n&&clearTimeout(n),null}var c=class{constructor(t){this.config=t}async validateDomain(){if(!this.config.appId)return{isValid:!1,reason:"AppId is required"};try{let t=d(this.config),i=window.location.hostname,o=await fetch(`${t}/api/AppConfig/validatedomain`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({appId:this.config.appId,domain:i})});if(!o.ok)return console.error("Domain validation request failed:",o.status),{isValid:!1,reason:"ValidationRequestFailed"};let{data:e}=await o.json();return{isValid:e.isValid,reason:e.reason,appConfig:e.appConfig}}catch(t){return console.error("Domain validation error:",t),{isValid:!1,reason:"NetworkError"}}}async validateAndUpdateConfig(){let t=await this.validateDomain();return t.isValid?(t.appConfig&&(this.config.organizationName=t.appConfig.organizationName,this.config.repositoryName=t.appConfig.repositoryName,this.config.title=t.appConfig.name||this.config.title),!0):(console.error("KoalaChatWidget: Domain validation failed -",t.reason),typeof this.config.onValidationFailed=="function"&&this.config.onValidationFailed(window.location.hostname),!1)}};var p=class{constructor(t){this.config=t}createFloatingButton(t){let i=document.createElement("button");if(i.className="koala-floating-button",i.title="\u6253\u5F00 AI \u52A9\u624B",this.config.expandIcon)i.style.backgroundImage=`url(data:image/png;base64,${this.config.expandIcon})`,i.style.backgroundSize="cover",i.style.backgroundPosition="center";else{let o=d(this.config);i.innerHTML=`<img src="${o}/logo.png" alt="AI \u52A9\u624B" style="width: 64px; height: 64px;">`}return i.addEventListener("click",t),i}createChatContainer(t,i,o){let e=document.createElement("div");e.className="koala-chat-container";let a=this.createHeader(t,i,o),r=document.createElement("div");return r.className="koala-chat-content",this.showLoading(r),e.appendChild(a),e.appendChild(r),e}createHeader(t,i,o){let e=document.createElement("div");e.className="koala-chat-header";let a=document.createElement("div");a.className="koala-header-title",a.innerHTML=`${this.config.title||"AI \u52A9\u624B"}`;let r=document.createElement("div");r.className="koala-header-actions";let f=this.createHeaderButton("\u2212","\u6700\u5C0F\u5316",t),u=this.createHeaderButton("\u26F6","\u6700\u5927\u5316",i),v=this.createCloseButton(o);return r.appendChild(f),r.appendChild(u),r.appendChild(v),e.appendChild(a),e.appendChild(r),e}createHeaderButton(t,i,o){let e=document.createElement("button");return e.className="koala-header-btn",e.innerHTML=t,e.title=i,e.addEventListener("click",o),e}createCloseButton(t){let i=document.createElement("button");if(i.className="koala-header-btn",i.title="\u5173\u95ED",this.config.closeIcon){let o=document.createElement("img");o.src=`data:image/png;base64,${this.config.closeIcon}`,o.alt="\u5173\u95ED",o.style.width="14px",o.style.height="14px",i.appendChild(o)}else i.innerHTML="\xD7";return i.addEventListener("click",t),i}showLoading(t){t.innerHTML=`
      <div class="koala-loading">
        <div>\u6B63\u5728\u52A0\u8F7D AI \u52A9\u624B...</div>
      </div>
    `}showError(t,i){t.innerHTML=`
      <div class="koala-error">
        <div class="koala-error-title">\u52A0\u8F7D\u5931\u8D25</div>
        <div class="koala-error-description">${i}</div>
      </div>
    `}loadChatInterface(t){if(!this.config.organizationName||!this.config.repositoryName){this.showError(t,"\u5E94\u7528\u914D\u7F6E\u7F3A\u5931\uFF0C\u65E0\u6CD5\u52A0\u8F7D\u804A\u5929\u754C\u9762");return}let i=d(this.config),o=new URLSearchParams({appId:this.config.appId,organizationName:this.config.organizationName,repositoryName:this.config.repositoryName,title:this.config.title||"AI \u52A9\u624B",theme:this.config.theme||"light",embedded:"true"});this.config.expandIcon&&o.set("expandIcon",this.config.expandIcon),this.config.closeIcon&&o.set("closeIcon",this.config.closeIcon);let e=`${i}/chat/embedded?${o.toString()}`,a=document.createElement("iframe");a.src=e,a.className="koala-iframe-container",a.frameBorder="0",a.setAttribute("allowtransparency","true"),t.innerHTML="",t.appendChild(a)}};var h=class{constructor(t){this.tooltipElement=null;this.tooltipTimer=null;this.tooltipHideTimer=null;this.lastTooltipHideTime=0;this.lastActivity=Date.now();this.floatingButton=null;this.isExpanded=!1;this.onToggleChat=null;this.config=t}setFloatingButton(t){this.floatingButton=t}setExpanded(t){this.isExpanded=t}initActivityListeners(t){let i=["mousedown","mousemove","keypress","scroll","touchstart","click"],o=()=>{let e=Date.now();e-this.lastActivity<1e3||(this.lastActivity=e,this.hideTooltip(),this.config.enableTooltip&&this.floatingButton&&!this.isExpanded&&this.startTooltipTimer())};i.forEach(e=>{document.addEventListener(e,o,{passive:!0,capture:!0})}),this.onToggleChat=t}startTooltipTimer(){this.tooltipTimer=l(this.tooltipTimer),this.tooltipHideTimer=l(this.tooltipHideTimer);let t=Date.now(),i=this.config.tooltipDelay||5e3;if(this.lastTooltipHideTime>0){let o=t-this.lastTooltipHideTime,e=this.config.tooltipRepeatDelay||3e4;o<e&&(i=e-o)}this.tooltipTimer=setTimeout(()=>{!this.isExpanded&&this.floatingButton&&this.showTooltip()},i)}showTooltip(){if(!(!this.config.enableTooltip||!this.config.tooltipText||this.isExpanded)&&(this.tooltipElement||this.createTooltip(),this.tooltipElement)){this.tooltipElement.textContent=this.config.tooltipText,this.tooltipElement.classList.add("visible");let t=this.config.tooltipDuration||3e3;t>0&&(this.tooltipHideTimer=setTimeout(()=>{this.hideTooltip()},t))}}hideTooltip(){this.tooltipElement&&this.tooltipElement.classList.remove("visible"),this.tooltipHideTimer=l(this.tooltipHideTimer),this.lastTooltipHideTime=Date.now()}createTooltip(){this.tooltipElement=document.createElement("div"),this.tooltipElement.className="koala-tooltip",document.body.appendChild(this.tooltipElement);let t=()=>{if(this.floatingButton&&this.tooltipElement){let i=this.floatingButton.getBoundingClientRect(),o=this.tooltipElement.getBoundingClientRect(),e=window.innerHeight-i.top+12,a=window.innerWidth-i.left-i.width/2-o.width/2;this.tooltipElement.style.bottom=e+"px",this.tooltipElement.style.right=Math.max(12,a)+"px"}};this.tooltipElement.addEventListener("click",i=>{i.stopPropagation(),this.onToggleChat&&this.onToggleChat()}),window.addEventListener("resize",t),requestAnimationFrame(t)}destroy(){this.tooltipTimer=l(this.tooltipTimer),this.tooltipHideTimer=l(this.tooltipHideTimer),this.tooltipElement&&(this.tooltipElement.remove(),this.tooltipElement=null),this.lastActivity=Date.now(),this.lastTooltipHideTime=0}};function g(){let n=".koala-chat-widget{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;direction:ltr}.koala-floating-button{position:fixed;bottom:24px;right:24px;width:56px;height:56px;border-radius:50%;background:#1677ff;border:none;box-shadow:0 4px 20px rgba(0,0,0,0.15);z-index:10000;cursor:pointer;display:flex;align-items:center;justify-content:center;color:white;font-size:24px;transition:transform 0.2s ease}.koala-floating-button:hover{transform:scale(1.05)}.koala-chat-container{position:fixed;bottom:24px;right:24px;width:550px;height:700px;background:white;border-radius:12px;box-shadow:0 8px 32px rgba(0,0,0,0.12);z-index:10001;display:none;flex-direction:column;overflow:hidden;border:1px solid #d9d9d9}.koala-chat-container.visible{display:flex}.koala-chat-container.minimized{height:56px}.koala-chat-container.maximized{width:550px;height:100vh;bottom:0;right:0;top:0}.koala-chat-header{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #d9d9d9;background:white;min-height:56px}.koala-header-title{display:flex;align-items:center;gap:8px;flex:1;font-size:16px;font-weight:600;margin:0;color:#262626}.koala-header-actions{display:flex;gap:4px}.koala-header-btn{width:24px;height:24px;border:none;background:none;cursor:pointer;border-radius:4px;display:flex;align-items:center;justify-content:center;color:#8c8c8c;transition:background-color 0.2s}.koala-header-btn:hover{background-color:#f5f5f5;color:#595959}.koala-chat-content{flex:1;display:flex;flex-direction:column;overflow:hidden}.koala-loading{display:flex;align-items:center;justify-content:center;padding:20px;color:#8c8c8c}.koala-error{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;color:#ff4d4f;text-align:center}.koala-error-title{font-weight:500;margin-bottom:8px}.koala-error-description{font-size:14px;color:#8c8c8c}.koala-iframe-container{flex:1;border:none;width:100%;height:100%}.koala-tooltip{position:fixed;background:rgba(0,0,0,0.8);color:white;padding:8px 12px;border-radius:8px;font-size:14px;white-space:nowrap;z-index:998;opacity:0;transform:translateY(10px);transition:opacity 0.3s ease,transform 0.3s ease;pointer-events:none}.koala-tooltip.visible{opacity:1;transform:translateY(0)}.koala-tooltip::after{content:'';position:absolute;top:100%;left:50%;transform:translateX(-50%);border:6px solid transparent;border-top-color:rgba(0,0,0,0.8)}@media (max-width:768px){.koala-chat-container{bottom:0;right:0;left:0;width:100%;height:100%;border-radius:0}.koala-chat-container.maximized{width:100%;height:100%}.koala-floating-button{bottom:20px;right:20px}.koala-tooltip{bottom:84px;right:20px;max-width:calc(100vw - 40px);white-space:pre-wrap}}",t=document.createElement("style");t.textContent=n,document.head.appendChild(t)}var m=class{constructor(){this.version="1.0.0";this.config={appId:"",organizationName:"default",repositoryName:"default",title:"AI \u52A9\u624B",expandIcon:"",closeIcon:"",apiUrl:"",theme:"light",enableTooltip:!0,tooltipText:"\u70B9\u51FB\u6211\u8BE2\u95EE\u60A8\u60F3\u77E5\u9053\u7684\uFF01",tooltipDelay:5e3,tooltipDuration:3e3,tooltipRepeatDelay:3e4};this.floatingButton=null;this.chatContainer=null;this.isExpanded=!1;this.isMinimized=!1;this.isMaximized=!1;this.isValidated=!1}async init(t){try{if(this.config={...this.config,...t},!this.config.appId){console.error("KoalaChatWidget: appId is required");return}if(this.validationService=new c(this.config),this.uiComponents=new p(this.config),this.tooltipManager=new h(this.config),!await this.validationService.validateAndUpdateConfig())return;this.isValidated=!0,document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>this.initWidget(),100)}):setTimeout(()=>this.initWidget(),100)}catch(i){console.error("KoalaChatWidget initialization failed:",i),typeof this.config.onError=="function"&&this.config.onError(i instanceof Error?i.message:String(i))}}initWidget(){try{g(),this.floatingButton=this.uiComponents.createFloatingButton(()=>this.toggle()),document.body.appendChild(this.floatingButton),this.tooltipManager.setFloatingButton(this.floatingButton),this.tooltipManager.initActivityListeners(()=>this.toggle()),this.config.enableTooltip&&this.tooltipManager.startTooltipTimer(),console.log("KoalaChatWidget initialized successfully")}catch(t){console.error("KoalaChatWidget initialization failed:",t),typeof this.config.onError=="function"&&this.config.onError(t instanceof Error?t.message:String(t))}}async open(){if(!this.isValidated){if(!await this.validationService.validateAndUpdateConfig())return;this.isValidated=!0}if(this.tooltipManager.hideTooltip(),!this.chatContainer){this.chatContainer=this.uiComponents.createChatContainer(()=>this.minimizeChat(),()=>this.toggleMaximize(),()=>this.close()),document.body.appendChild(this.chatContainer);let t=this.chatContainer.querySelector(".koala-chat-content");t&&this.uiComponents.loadChatInterface(t)}this.chatContainer.classList.add("visible"),this.floatingButton&&(this.floatingButton.style.display="none"),this.isExpanded=!0,this.isMinimized=!1,this.tooltipManager.setExpanded(!0)}close(){this.chatContainer&&(this.chatContainer.classList.remove("visible"),this.chatContainer.classList.remove("minimized"),this.chatContainer.classList.remove("maximized")),this.floatingButton&&(this.floatingButton.style.display="flex"),this.isExpanded=!1,this.isMinimized=!1,this.isMaximized=!1,this.tooltipManager.setExpanded(!1),this.config.enableTooltip&&this.tooltipManager.startTooltipTimer()}async toggle(){this.isExpanded?this.close():await this.open()}minimizeChat(){this.chatContainer&&(this.chatContainer.classList.add("minimized"),this.isMinimized=!0)}toggleMaximize(){this.chatContainer&&(this.isMaximized?(this.chatContainer.classList.remove("maximized"),this.isMaximized=!1):(this.chatContainer.classList.add("maximized"),this.isMaximized=!0))}destroy(){this.floatingButton&&(this.floatingButton.remove(),this.floatingButton=null),this.chatContainer&&(this.chatContainer.remove(),this.chatContainer=null),this.tooltipManager?.destroy(),this.isExpanded=!1,this.isMinimized=!1,this.isMaximized=!1,this.isValidated=!1}};var s=new m;window.KoalaChatWidget={init:s.init.bind(s),destroy:s.destroy.bind(s),open:s.open.bind(s),close:s.close.bind(s),toggle:s.toggle.bind(s),version:s.version};})();
//# sourceMappingURL=koala-chat-widget.js.map
