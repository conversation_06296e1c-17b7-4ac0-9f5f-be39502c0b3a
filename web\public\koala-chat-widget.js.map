{"version": 3, "sources": ["../widget/utils.ts", "../widget/validation.ts", "../widget/ui.ts", "../widget/tooltip.ts", "../widget/styles.ts", "../widget/widget.ts", "../widget/index.ts"], "sourcesContent": ["import type { KoalaChatWidgetConfig } from \"./types\";\r\n\r\n/**\r\n * 获取API URL的辅助函数\r\n */\r\nexport function getApiUrl(config: KoalaChatWidgetConfig): string {\r\n  // 如果配置中有apiUrl，直接使用\r\n  if (config.apiUrl) {\r\n    return config.apiUrl;\r\n  }\r\n\r\n  // 尝试从脚本标签获取源域名\r\n  const scriptElement = document.querySelector(\r\n    'script[src*=\"koala-chat-widget.js\"]'\r\n  );\r\n  if (scriptElement) {\r\n    const scriptSrc = scriptElement.getAttribute(\"src\");\r\n    if (scriptSrc) {\r\n      try {\r\n        const url = new URL(scriptSrc, window.location.href);\r\n        return url.origin;\r\n      } catch (e) {\r\n        console.warn(\"Unable to parse script source URL:\", scriptSrc);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 兜底使用当前页面域名\r\n  return window.location.origin;\r\n}\r\n\r\n/**\r\n * 防抖函数\r\n */\r\nexport function debounce<T extends (...args: any[]) => any>(\r\n  func: T,\r\n  wait: number\r\n): (...args: Parameters<T>) => void {\r\n  let timeout: NodeJS.Timeout | null = null;\r\n\r\n  return (...args: Parameters<T>) => {\r\n    if (timeout) {\r\n      clearTimeout(timeout);\r\n    }\r\n    timeout = setTimeout(() => func(...args), wait);\r\n  };\r\n}\r\n\r\n/**\r\n * 清理定时器\r\n */\r\nexport function clearTimer(timer: NodeJS.Timeout | null): null {\r\n  if (timer) {\r\n    clearTimeout(timer);\r\n  }\r\n  return null;\r\n}\r\n", "import type { KoalaChatWidgetConfig, ValidationResponse } from \"./types\";\r\nimport { getApiUrl } from \"./utils\";\r\n\r\n/**\r\n * 域名验证服务\r\n */\r\nexport class ValidationService {\r\n  private config: KoalaChatWidgetConfig;\r\n\r\n  constructor(config: KoalaChatWidgetConfig) {\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * 验证域名\r\n   */\r\n  async validateDomain(): Promise<ValidationResponse> {\r\n    if (!this.config.appId) {\r\n      return { isValid: false, reason: \"AppId is required\" };\r\n    }\r\n\r\n    try {\r\n      const apiUrl = getApiUrl(this.config);\r\n      const currentDomain = window.location.hostname;\r\n\r\n      const response = await fetch(`${apiUrl}/api/AppConfig/validatedomain`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          appId: this.config.appId,\r\n          domain: currentDomain,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        console.error(\"Domain validation request failed:\", response.status);\r\n        return { isValid: false, reason: \"ValidationRequestFailed\" };\r\n      }\r\n\r\n      const { data } = await response.json();\r\n      return {\r\n        isValid: data.isValid,\r\n        reason: data.reason,\r\n        appConfig: data.appConfig,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Domain validation error:\", error);\r\n      return { isValid: false, reason: \"NetworkError\" };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 执行域名验证并更新配置\r\n   */\r\n  async validateAndUpdateConfig(): Promise<boolean> {\r\n    const validation = await this.validateDomain();\r\n    if (!validation.isValid) {\r\n      console.error(\r\n        \"KoalaChatWidget: Domain validation failed -\",\r\n        validation.reason\r\n      );\r\n      if (typeof this.config.onValidationFailed === \"function\") {\r\n        this.config.onValidationFailed(window.location.hostname);\r\n      }\r\n      return false;\r\n    }\r\n\r\n    // 更新配置（如果从验证结果中获取到了应用配置）\r\n    if (validation.appConfig) {\r\n      this.config.organizationName = validation.appConfig.organizationName;\r\n      this.config.repositoryName = validation.appConfig.repositoryName;\r\n      this.config.title = validation.appConfig.name || this.config.title;\r\n    }\r\n\r\n    return true;\r\n  }\r\n}\r\n", "import type { KoalaChatWidgetConfig } from \"./types\";\r\nimport { getApiUrl } from \"./utils\";\r\n\r\n/**\r\n * UI 组件管理类\r\n */\r\nexport class UIComponents {\r\n  private config: KoalaChatWidgetConfig;\r\n\r\n  constructor(config: KoalaChatWidgetConfig) {\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * 创建悬浮球按钮\r\n   */\r\n  createFloatingButton(onClick: () => void): HTMLButtonElement {\r\n    const button = document.createElement(\"button\");\r\n    button.className = \"koala-floating-button\";\r\n    button.title = \"打开 AI 助手\";\r\n\r\n    if (this.config.expandIcon) {\r\n      button.style.backgroundImage = `url(data:image/png;base64,${this.config.expandIcon})`;\r\n      button.style.backgroundSize = \"cover\";\r\n      button.style.backgroundPosition = \"center\";\r\n    } else {\r\n      const baseUrl = getApiUrl(this.config);\r\n      button.innerHTML = `<img src=\"${baseUrl}/logo.png\" alt=\"AI 助手\" style=\"width: 64px; height: 64px;\">`;\r\n    }\r\n\r\n    button.addEventListener(\"click\", onClick);\r\n    return button;\r\n  }\r\n\r\n  /**\r\n   * 创建聊天容器\r\n   */\r\n  createChatContainer(\r\n    onMinimize: () => void,\r\n    onMaximize: () => void,\r\n    onClose: () => void\r\n  ): HTMLDivElement {\r\n    const container = document.createElement(\"div\");\r\n    container.className = \"koala-chat-container\";\r\n\r\n    // 创建头部\r\n    const header = this.createHeader(onMinimize, onMaximize, onClose);\r\n\r\n    // 创建内容区域\r\n    const content = document.createElement(\"div\");\r\n    content.className = \"koala-chat-content\";\r\n    this.showLoading(content);\r\n\r\n    container.appendChild(header);\r\n    container.appendChild(content);\r\n\r\n    return container;\r\n  }\r\n\r\n  /**\r\n   * 创建头部\r\n   */\r\n  private createHeader(\r\n    onMinimize: () => void,\r\n    onMaximize: () => void,\r\n    onClose: () => void\r\n  ): HTMLDivElement {\r\n    const header = document.createElement(\"div\");\r\n    header.className = \"koala-chat-header\";\r\n\r\n    const title = document.createElement(\"div\");\r\n    title.className = \"koala-header-title\";\r\n    title.innerHTML = `${this.config.title || \"AI 助手\"}`;\r\n\r\n    const actions = document.createElement(\"div\");\r\n    actions.className = \"koala-header-actions\";\r\n\r\n    // 最小化按钮\r\n    const minimizeBtn = this.createHeaderButton(\"−\", \"最小化\", onMinimize);\r\n\r\n    // 最大化按钮\r\n    const maximizeBtn = this.createHeaderButton(\"⛶\", \"最大化\", onMaximize);\r\n\r\n    // 关闭按钮\r\n    const closeBtn = this.createCloseButton(onClose);\r\n\r\n    actions.appendChild(minimizeBtn);\r\n    actions.appendChild(maximizeBtn);\r\n    actions.appendChild(closeBtn);\r\n\r\n    header.appendChild(title);\r\n    header.appendChild(actions);\r\n\r\n    return header;\r\n  }\r\n\r\n  /**\r\n   * 创建头部按钮\r\n   */\r\n  private createHeaderButton(\r\n    content: string,\r\n    title: string,\r\n    onClick: () => void\r\n  ): HTMLButtonElement {\r\n    const button = document.createElement(\"button\");\r\n    button.className = \"koala-header-btn\";\r\n    button.innerHTML = content;\r\n    button.title = title;\r\n    button.addEventListener(\"click\", onClick);\r\n    return button;\r\n  }\r\n\r\n  /**\r\n   * 创建关闭按钮\r\n   */\r\n  private createCloseButton(onClick: () => void): HTMLButtonElement {\r\n    const closeBtn = document.createElement(\"button\");\r\n    closeBtn.className = \"koala-header-btn\";\r\n    closeBtn.title = \"关闭\";\r\n\r\n    if (this.config.closeIcon) {\r\n      const img = document.createElement(\"img\");\r\n      img.src = `data:image/png;base64,${this.config.closeIcon}`;\r\n      img.alt = \"关闭\";\r\n      img.style.width = \"14px\";\r\n      img.style.height = \"14px\";\r\n      closeBtn.appendChild(img);\r\n    } else {\r\n      closeBtn.innerHTML = \"×\";\r\n    }\r\n\r\n    closeBtn.addEventListener(\"click\", onClick);\r\n    return closeBtn;\r\n  }\r\n\r\n  /**\r\n   * 显示加载状态\r\n   */\r\n  showLoading(container: HTMLElement): void {\r\n    container.innerHTML = `\r\n      <div class=\"koala-loading\">\r\n        <div>正在加载 AI 助手...</div>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * 显示错误状态\r\n   */\r\n  showError(container: HTMLElement, message: string): void {\r\n    container.innerHTML = `\r\n      <div class=\"koala-error\">\r\n        <div class=\"koala-error-title\">加载失败</div>\r\n        <div class=\"koala-error-description\">${message}</div>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * 加载聊天界面\r\n   */\r\n  loadChatInterface(container: HTMLElement): void {\r\n    // 检查必要配置\r\n    if (!this.config.organizationName || !this.config.repositoryName) {\r\n      this.showError(container, \"应用配置缺失，无法加载聊天界面\");\r\n      return;\r\n    }\r\n\r\n    // 获取API URL用于构建聊天界面URL\r\n    const baseUrl = getApiUrl(this.config);\r\n\r\n    // 构建聊天界面 URL\r\n    const params = new URLSearchParams({\r\n      appId: this.config.appId,\r\n      organizationName: this.config.organizationName,\r\n      repositoryName: this.config.repositoryName,\r\n      title: this.config.title || \"AI 助手\",\r\n      theme: this.config.theme || \"light\",\r\n      embedded: \"true\",\r\n    });\r\n\r\n    if (this.config.expandIcon) {\r\n      params.set(\"expandIcon\", this.config.expandIcon);\r\n    }\r\n    if (this.config.closeIcon) {\r\n      params.set(\"closeIcon\", this.config.closeIcon);\r\n    }\r\n\r\n    const chatUrl = `${baseUrl}/chat/embedded?${params.toString()}`;\r\n\r\n    // 创建 iframe\r\n    const iframe = document.createElement(\"iframe\");\r\n    iframe.src = chatUrl;\r\n    iframe.className = \"koala-iframe-container\";\r\n    iframe.frameBorder = \"0\";\r\n    iframe.setAttribute(\"allowtransparency\", \"true\");\r\n\r\n    // 直接插入iframe到容器中\r\n    container.innerHTML = \"\";\r\n    container.appendChild(iframe);\r\n  }\r\n}\r\n", "import type { KoalaChatWidgetConfig } from \"./types\";\r\nimport { clearTimer } from \"./utils\";\r\n\r\n/**\r\n * 提示功能管理类\r\n */\r\nexport class TooltipManager {\r\n  private config: KoalaChatWidgetConfig;\r\n  private tooltipElement: HTMLElement | null = null;\r\n  private tooltipTimer: NodeJS.Timeout | null = null;\r\n  private tooltipHideTimer: NodeJS.Timeout | null = null;\r\n  private lastTooltipHideTime: number = 0;\r\n  private lastActivity: number = Date.now();\r\n  private floatingButton: HTMLElement | null = null;\r\n  private isExpanded: boolean = false;\r\n\r\n  constructor(config: KoalaChatWidgetConfig) {\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * 设置悬浮球引用\r\n   */\r\n  setFloatingButton(button: HTMLElement): void {\r\n    this.floatingButton = button;\r\n  }\r\n\r\n  /**\r\n   * 设置展开状态\r\n   */\r\n  setExpanded(expanded: boolean): void {\r\n    this.isExpanded = expanded;\r\n  }\r\n\r\n  /**\r\n   * 初始化活动监听器\r\n   */\r\n  initActivityListeners(onToggleChat: () => void): void {\r\n    const events = [\r\n      \"mousedown\",\r\n      \"mousemove\",\r\n      \"keypress\",\r\n      \"scroll\",\r\n      \"touchstart\",\r\n      \"click\",\r\n    ];\r\n\r\n    const updateUserActivity = () => {\r\n      const now = Date.now();\r\n\r\n      // 如果距离上次活动时间太短，避免频繁重置\r\n      if (now - this.lastActivity < 1000) {\r\n        return;\r\n      }\r\n\r\n      this.lastActivity = now;\r\n      this.hideTooltip();\r\n\r\n      // 重新开始计时\r\n      if (\r\n        this.config.enableTooltip &&\r\n        this.floatingButton &&\r\n        !this.isExpanded\r\n      ) {\r\n        this.startTooltipTimer();\r\n      }\r\n    };\r\n\r\n    events.forEach((event) => {\r\n      document.addEventListener(event, updateUserActivity, {\r\n        passive: true,\r\n        capture: true,\r\n      });\r\n    });\r\n\r\n    // 设置点击提示打开聊天的回调\r\n    this.onToggleChat = onToggleChat;\r\n  }\r\n\r\n  private onToggleChat: (() => void) | null = null;\r\n\r\n  /**\r\n   * 开始提示计时器\r\n   */\r\n  startTooltipTimer(): void {\r\n    this.tooltipTimer = clearTimer(this.tooltipTimer);\r\n    this.tooltipHideTimer = clearTimer(this.tooltipHideTimer);\r\n\r\n    // 检查是否需要等待更长时间（如果提示之前显示过）\r\n    const now = Date.now();\r\n    let delay = this.config.tooltipDelay || 5000;\r\n\r\n    if (this.lastTooltipHideTime > 0) {\r\n      const timeSinceHide = now - this.lastTooltipHideTime;\r\n      const repeatDelay = this.config.tooltipRepeatDelay || 30000;\r\n      if (timeSinceHide < repeatDelay) {\r\n        delay = repeatDelay - timeSinceHide;\r\n      }\r\n    }\r\n\r\n    this.tooltipTimer = setTimeout(() => {\r\n      if (!this.isExpanded && this.floatingButton) {\r\n        this.showTooltip();\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * 显示提示\r\n   */\r\n  private showTooltip(): void {\r\n    if (\r\n      !this.config.enableTooltip ||\r\n      !this.config.tooltipText ||\r\n      this.isExpanded\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (!this.tooltipElement) {\r\n      this.createTooltip();\r\n    }\r\n\r\n    if (this.tooltipElement) {\r\n      this.tooltipElement.textContent = this.config.tooltipText;\r\n      this.tooltipElement.classList.add(\"visible\");\r\n\r\n      // 设置自动隐藏计时器\r\n      const duration = this.config.tooltipDuration || 3000;\r\n      if (duration > 0) {\r\n        this.tooltipHideTimer = setTimeout(() => {\r\n          this.hideTooltip();\r\n        }, duration);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 隐藏提示\r\n   */\r\n  hideTooltip(): void {\r\n    if (this.tooltipElement) {\r\n      this.tooltipElement.classList.remove(\"visible\");\r\n    }\r\n    this.tooltipHideTimer = clearTimer(this.tooltipHideTimer);\r\n    this.lastTooltipHideTime = Date.now();\r\n  }\r\n\r\n  /**\r\n   * 创建提示元素\r\n   */\r\n  private createTooltip(): void {\r\n    this.tooltipElement = document.createElement(\"div\");\r\n    this.tooltipElement.className = \"koala-tooltip\";\r\n    document.body.appendChild(this.tooltipElement);\r\n\r\n    // 动态计算位置，确保提示出现在悬浮球上方\r\n    const updateTooltipPosition = () => {\r\n      if (this.floatingButton && this.tooltipElement) {\r\n        const buttonRect = this.floatingButton.getBoundingClientRect();\r\n        const tooltipRect = this.tooltipElement.getBoundingClientRect();\r\n\r\n        // 计算提示位置：悬浮球上方，水平居中\r\n        const tooltipBottom = window.innerHeight - buttonRect.top + 12; // 12px间距\r\n        const tooltipRight =\r\n          window.innerWidth -\r\n          buttonRect.left -\r\n          buttonRect.width / 2 -\r\n          tooltipRect.width / 2;\r\n\r\n        this.tooltipElement.style.bottom = tooltipBottom + \"px\";\r\n        this.tooltipElement.style.right = Math.max(12, tooltipRight) + \"px\";\r\n      }\r\n    };\r\n\r\n    // 点击提示时也打开聊天\r\n    this.tooltipElement.addEventListener(\"click\", (e) => {\r\n      e.stopPropagation();\r\n      if (this.onToggleChat) {\r\n        this.onToggleChat();\r\n      }\r\n    });\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener(\"resize\", updateTooltipPosition);\r\n\r\n    // 初始位置更新\r\n    requestAnimationFrame(updateTooltipPosition);\r\n  }\r\n\r\n  /**\r\n   * 销毁提示管理器\r\n   */\r\n  destroy(): void {\r\n    this.tooltipTimer = clearTimer(this.tooltipTimer);\r\n    this.tooltipHideTimer = clearTimer(this.tooltipHideTimer);\r\n\r\n    if (this.tooltipElement) {\r\n      this.tooltipElement.remove();\r\n      this.tooltipElement = null;\r\n    }\r\n\r\n    this.lastActivity = Date.now();\r\n    this.lastTooltipHideTime = 0;\r\n  }\r\n}\r\n", "/**\r\n * 创建聊天组件的样式\r\n */\r\nexport function createStyles(): void {\r\n  const styles = `.koala-chat-widget{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Can<PERSON>ell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;direction:ltr}.koala-floating-button{position:fixed;bottom:24px;right:24px;width:56px;height:56px;border-radius:50%;background:#1677ff;border:none;box-shadow:0 4px 20px rgba(0,0,0,0.15);z-index:10000;cursor:pointer;display:flex;align-items:center;justify-content:center;color:white;font-size:24px;transition:transform 0.2s ease}.koala-floating-button:hover{transform:scale(1.05)}.koala-chat-container{position:fixed;bottom:24px;right:24px;width:550px;height:700px;background:white;border-radius:12px;box-shadow:0 8px 32px rgba(0,0,0,0.12);z-index:10001;display:none;flex-direction:column;overflow:hidden;border:1px solid #d9d9d9}.koala-chat-container.visible{display:flex}.koala-chat-container.minimized{height:56px}.koala-chat-container.maximized{width:550px;height:100vh;bottom:0;right:0;top:0}.koala-chat-header{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;border-bottom:1px solid #d9d9d9;background:white;min-height:56px}.koala-header-title{display:flex;align-items:center;gap:8px;flex:1;font-size:16px;font-weight:600;margin:0;color:#262626}.koala-header-actions{display:flex;gap:4px}.koala-header-btn{width:24px;height:24px;border:none;background:none;cursor:pointer;border-radius:4px;display:flex;align-items:center;justify-content:center;color:#8c8c8c;transition:background-color 0.2s}.koala-header-btn:hover{background-color:#f5f5f5;color:#595959}.koala-chat-content{flex:1;display:flex;flex-direction:column;overflow:hidden}.koala-loading{display:flex;align-items:center;justify-content:center;padding:20px;color:#8c8c8c}.koala-error{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;color:#ff4d4f;text-align:center}.koala-error-title{font-weight:500;margin-bottom:8px}.koala-error-description{font-size:14px;color:#8c8c8c}.koala-iframe-container{flex:1;border:none;width:100%;height:100%}.koala-tooltip{position:fixed;background:rgba(0,0,0,0.8);color:white;padding:8px 12px;border-radius:8px;font-size:14px;white-space:nowrap;z-index:998;opacity:0;transform:translateY(10px);transition:opacity 0.3s ease,transform 0.3s ease;pointer-events:none}.koala-tooltip.visible{opacity:1;transform:translateY(0)}.koala-tooltip::after{content:'';position:absolute;top:100%;left:50%;transform:translateX(-50%);border:6px solid transparent;border-top-color:rgba(0,0,0,0.8)}@media (max-width:768px){.koala-chat-container{bottom:0;right:0;left:0;width:100%;height:100%;border-radius:0}.koala-chat-container.maximized{width:100%;height:100%}.koala-floating-button{bottom:20px;right:20px}.koala-tooltip{bottom:84px;right:20px;max-width:calc(100vw - 40px);white-space:pre-wrap}}`;\r\n\r\n  const styleElement = document.createElement(\"style\");\r\n  styleElement.textContent = styles;\r\n  document.head.appendChild(styleElement);\r\n}\r\n", "import type { KoalaChatWidgetConfig, KoalaChatWidgetAPI } from \"./types\";\r\nimport { ValidationService } from \"./validation\";\r\nimport { UIComponents } from \"./ui\";\r\nimport { TooltipManager } from \"./tooltip\";\r\nimport { createStyles } from \"./styles\";\r\n\r\n/**\r\n * KoalaWiki AI Chat Widget 主类\r\n */\r\nexport class KoalaChatWidget implements KoalaChatWidgetAPI {\r\n  version = \"1.0.0\";\r\n\r\n  private config: KoalaChatWidgetConfig = {\r\n    appId: \"\",\r\n    organizationName: \"default\",\r\n    repositoryName: \"default\",\r\n    title: \"AI 助手\",\r\n    expandIcon: \"\",\r\n    closeIcon: \"\",\r\n    apiUrl: \"\",\r\n    theme: \"light\",\r\n    enableTooltip: true,\r\n    tooltipText: \"点击我询问您想知道的！\",\r\n    tooltipDelay: 5000,\r\n    tooltipDuration: 3000,\r\n    tooltipRepeatDelay: 30000,\r\n  };\r\n\r\n  private validationService!: ValidationService;\r\n  private uiComponents!: UIComponents;\r\n  private tooltipManager!: TooltipManager;\r\n\r\n  private floatingButton: HTMLButtonElement | null = null;\r\n  private chatContainer: HTMLDivElement | null = null;\r\n  private isExpanded: boolean = false;\r\n  private isMinimized: boolean = false;\r\n  private isMaximized: boolean = false;\r\n  private isValidated: boolean = false;\r\n\r\n  /**\r\n   * 初始化聊天组件\r\n   */\r\n  async init(options: KoalaChatWidgetConfig): Promise<void> {\r\n    try {\r\n      // 合并配置\r\n      this.config = { ...this.config, ...options };\r\n\r\n      // 验证必要参数\r\n      if (!this.config.appId) {\r\n        console.error(\"KoalaChatWidget: appId is required\");\r\n        return;\r\n      }\r\n\r\n      // 初始化服务\r\n      this.validationService = new ValidationService(this.config);\r\n      this.uiComponents = new UIComponents(this.config);\r\n      this.tooltipManager = new TooltipManager(this.config);\r\n\r\n      // 验证域名并获取应用配置\r\n      const success = await this.validationService.validateAndUpdateConfig();\r\n      if (!success) {\r\n        return;\r\n      }\r\n\r\n      // 标记为已验证\r\n      this.isValidated = true;\r\n\r\n      // 等待 DOM 加载完成\r\n      if (document.readyState === \"loading\") {\r\n        document.addEventListener(\"DOMContentLoaded\", () => {\r\n          setTimeout(() => this.initWidget(), 100);\r\n        });\r\n      } else {\r\n        setTimeout(() => this.initWidget(), 100);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"KoalaChatWidget initialization failed:\", error);\r\n      if (typeof this.config.onError === \"function\") {\r\n        this.config.onError(\r\n          error instanceof Error ? error.message : String(error)\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 初始化组件\r\n   */\r\n  private initWidget(): void {\r\n    try {\r\n      // 创建样式\r\n      createStyles();\r\n\r\n      // 创建悬浮球按钮\r\n      this.floatingButton = this.uiComponents.createFloatingButton(() =>\r\n        this.toggle()\r\n      );\r\n      document.body.appendChild(this.floatingButton);\r\n\r\n      // 设置提示管理器\r\n      this.tooltipManager.setFloatingButton(this.floatingButton);\r\n      this.tooltipManager.initActivityListeners(() => this.toggle());\r\n\r\n      // 启动提示计时器\r\n      if (this.config.enableTooltip) {\r\n        this.tooltipManager.startTooltipTimer();\r\n      }\r\n\r\n      console.log(\"KoalaChatWidget initialized successfully\");\r\n    } catch (error) {\r\n      console.error(\"KoalaChatWidget initialization failed:\", error);\r\n      if (typeof this.config.onError === \"function\") {\r\n        this.config.onError(\r\n          error instanceof Error ? error.message : String(error)\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 打开聊天窗口\r\n   */\r\n  async open(): Promise<void> {\r\n    // 如果尚未验证，先进行验证\r\n    if (!this.isValidated) {\r\n      const success = await this.validationService.validateAndUpdateConfig();\r\n      if (!success) {\r\n        return;\r\n      }\r\n      this.isValidated = true;\r\n    }\r\n\r\n    // 隐藏提示\r\n    this.tooltipManager.hideTooltip();\r\n\r\n    if (!this.chatContainer) {\r\n      this.chatContainer = this.uiComponents.createChatContainer(\r\n        () => this.minimizeChat(),\r\n        () => this.toggleMaximize(),\r\n        () => this.close()\r\n      );\r\n      document.body.appendChild(this.chatContainer);\r\n\r\n      const contentElement = this.chatContainer.querySelector(\r\n        \".koala-chat-content\"\r\n      );\r\n      if (contentElement) {\r\n        this.uiComponents.loadChatInterface(contentElement as HTMLElement);\r\n      }\r\n    }\r\n\r\n    this.chatContainer.classList.add(\"visible\");\r\n    if (this.floatingButton) {\r\n      this.floatingButton.style.display = \"none\";\r\n    }\r\n    this.isExpanded = true;\r\n    this.isMinimized = false;\r\n    this.tooltipManager.setExpanded(true);\r\n  }\r\n\r\n  /**\r\n   * 关闭聊天窗口\r\n   */\r\n  close(): void {\r\n    if (this.chatContainer) {\r\n      this.chatContainer.classList.remove(\"visible\");\r\n      this.chatContainer.classList.remove(\"minimized\");\r\n      this.chatContainer.classList.remove(\"maximized\");\r\n    }\r\n    if (this.floatingButton) {\r\n      this.floatingButton.style.display = \"flex\";\r\n    }\r\n    this.isExpanded = false;\r\n    this.isMinimized = false;\r\n    this.isMaximized = false;\r\n    this.tooltipManager.setExpanded(false);\r\n\r\n    // 重新开始提示计时器\r\n    if (this.config.enableTooltip) {\r\n      this.tooltipManager.startTooltipTimer();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 切换聊天窗口状态\r\n   */\r\n  async toggle(): Promise<void> {\r\n    if (this.isExpanded) {\r\n      this.close();\r\n    } else {\r\n      await this.open();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 最小化聊天窗口\r\n   */\r\n  private minimizeChat(): void {\r\n    if (this.chatContainer) {\r\n      this.chatContainer.classList.add(\"minimized\");\r\n      this.isMinimized = true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 切换最大化\r\n   */\r\n  private toggleMaximize(): void {\r\n    if (this.chatContainer) {\r\n      if (this.isMaximized) {\r\n        this.chatContainer.classList.remove(\"maximized\");\r\n        this.isMaximized = false;\r\n      } else {\r\n        this.chatContainer.classList.add(\"maximized\");\r\n        this.isMaximized = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 销毁组件\r\n   */\r\n  destroy(): void {\r\n    // 移除元素\r\n    if (this.floatingButton) {\r\n      this.floatingButton.remove();\r\n      this.floatingButton = null;\r\n    }\r\n    if (this.chatContainer) {\r\n      this.chatContainer.remove();\r\n      this.chatContainer = null;\r\n    }\r\n\r\n    // 销毁提示管理器\r\n    this.tooltipManager?.destroy();\r\n\r\n    // 重置状态\r\n    this.isExpanded = false;\r\n    this.isMinimized = false;\r\n    this.isMaximized = false;\r\n    this.isValidated = false;\r\n  }\r\n}\r\n", "/**\r\n * KoalaWiki AI Chat Widget\r\n * 第三方网站引用的聊天悬浮球脚本\r\n *\r\n * 使用方式:\r\n * <script src=\"https://your-domain.com/koala-chat-widget.js\"></script>\r\n * <script>\r\n *   KoalaChatWidget.init({\r\n *     appId: 'your-app-id',  // 必填：应用ID，用于验证域名\r\n *     apiUrl: 'https://opendeep.wiki',  // 必填：API服务器地址\r\n *\r\n *     // 以下参数都是可选的\r\n *     title: '我的 AI 助手',         // 可选：标题（可能被验证接口返回的配置覆盖）\r\n *     theme: 'light',                 // 可选：主题 'light'|'dark'\r\n *\r\n *     // UI自定义\r\n *     expandIcon: null, // 可选：展开图标\r\n *     closeIcon: null,  // 可选：关闭图标\r\n *   });\r\n * </script>\r\n */\r\n\r\nimport { KoalaChatWidget } from \"./widget\";\r\n\r\n// 创建全局实例\r\nconst chatWidget = new KoalaChatWidget();\r\n\r\n// 暴露全局 API\r\nwindow.KoalaChatWidget = {\r\n  init: chatWidget.init.bind(chatWidget),\r\n  destroy: chatWidget.destroy.bind(chatWidget),\r\n  open: chatWidget.open.bind(chatWidget),\r\n  close: chatWidget.close.bind(chatWidget),\r\n  toggle: chatWidget.toggle.bind(chatWidget),\r\n  version: chatWidget.version,\r\n};\r\n"], "mappings": "MAKO,SAASA,EAAUC,EAAuC,CAE/D,GAAIA,EAAO,OACT,OAAOA,EAAO,OAIhB,IAAMC,EAAgB,SAAS,cAC7B,qCACF,EACA,GAAIA,EAAe,CACjB,IAAMC,EAAYD,EAAc,aAAa,KAAK,EAClD,GAAIC,EACF,GAAI,CAEF,OADY,IAAI,IAAIA,EAAW,OAAO,SAAS,IAAI,EACxC,MACb,MAAY,CACV,QAAQ,KAAK,qCAAsCA,CAAS,CAC9D,CAEJ,CAGA,OAAO,OAAO,SAAS,MACzB,CAsBO,SAASC,EAAWC,EAAoC,CAC7D,OAAIA,GACF,aAAaA,CAAK,EAEb,IACT,CClDO,IAAMC,EAAN,KAAwB,CAG7B,YAAYC,EAA+B,CACzC,KAAK,OAASA,CAChB,CAKA,MAAM,gBAA8C,CAClD,GAAI,CAAC,KAAK,OAAO,MACf,MAAO,CAAE,QAAS,GAAO,OAAQ,mBAAoB,EAGvD,GAAI,CACF,IAAMC,EAASC,EAAU,KAAK,MAAM,EAC9BC,EAAgB,OAAO,SAAS,SAEhCC,EAAW,MAAM,MAAM,GAAGH,CAAM,gCAAiC,CACrE,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CACnB,MAAO,KAAK,OAAO,MACnB,OAAQE,CACV,CAAC,CACH,CAAC,EAED,GAAI,CAACC,EAAS,GACZ,eAAQ,MAAM,oCAAqCA,EAAS,MAAM,EAC3D,CAAE,QAAS,GAAO,OAAQ,yBAA0B,EAG7D,GAAM,CAAE,KAAAC,CAAK,EAAI,MAAMD,EAAS,KAAK,EACrC,MAAO,CACL,QAASC,EAAK,QACd,OAAQA,EAAK,OACb,UAAWA,EAAK,SAClB,CACF,OAASC,EAAO,CACd,eAAQ,MAAM,2BAA4BA,CAAK,EACxC,CAAE,QAAS,GAAO,OAAQ,cAAe,CAClD,CACF,CAKA,MAAM,yBAA4C,CAChD,IAAMC,EAAa,MAAM,KAAK,eAAe,EAC7C,OAAKA,EAAW,SAYZA,EAAW,YACb,KAAK,OAAO,iBAAmBA,EAAW,UAAU,iBACpD,KAAK,OAAO,eAAiBA,EAAW,UAAU,eAClD,KAAK,OAAO,MAAQA,EAAW,UAAU,MAAQ,KAAK,OAAO,OAGxD,KAjBL,QAAQ,MACN,8CACAA,EAAW,MACb,EACI,OAAO,KAAK,OAAO,oBAAuB,YAC5C,KAAK,OAAO,mBAAmB,OAAO,SAAS,QAAQ,EAElD,GAWX,CACF,ECxEO,IAAMC,EAAN,KAAmB,CAGxB,YAAYC,EAA+B,CACzC,KAAK,OAASA,CAChB,CAKA,qBAAqBC,EAAwC,CAC3D,IAAMC,EAAS,SAAS,cAAc,QAAQ,EAI9C,GAHAA,EAAO,UAAY,wBACnBA,EAAO,MAAQ,+BAEX,KAAK,OAAO,WACdA,EAAO,MAAM,gBAAkB,6BAA6B,KAAK,OAAO,UAAU,IAClFA,EAAO,MAAM,eAAiB,QAC9BA,EAAO,MAAM,mBAAqB,aAC7B,CACL,IAAMC,EAAUC,EAAU,KAAK,MAAM,EACrCF,EAAO,UAAY,aAAaC,CAAO,sEACzC,CAEA,OAAAD,EAAO,iBAAiB,QAASD,CAAO,EACjCC,CACT,CAKA,oBACEG,EACAC,EACAC,EACgB,CAChB,IAAMC,EAAY,SAAS,cAAc,KAAK,EAC9CA,EAAU,UAAY,uBAGtB,IAAMC,EAAS,KAAK,aAAaJ,EAAYC,EAAYC,CAAO,EAG1DG,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,UAAY,qBACpB,KAAK,YAAYA,CAAO,EAExBF,EAAU,YAAYC,CAAM,EAC5BD,EAAU,YAAYE,CAAO,EAEtBF,CACT,CAKQ,aACNH,EACAC,EACAC,EACgB,CAChB,IAAME,EAAS,SAAS,cAAc,KAAK,EAC3CA,EAAO,UAAY,oBAEnB,IAAME,EAAQ,SAAS,cAAc,KAAK,EAC1CA,EAAM,UAAY,qBAClBA,EAAM,UAAY,GAAG,KAAK,OAAO,OAAS,iBAAO,GAEjD,IAAMC,EAAU,SAAS,cAAc,KAAK,EAC5CA,EAAQ,UAAY,uBAGpB,IAAMC,EAAc,KAAK,mBAAmB,SAAK,qBAAOR,CAAU,EAG5DS,EAAc,KAAK,mBAAmB,SAAK,qBAAOR,CAAU,EAG5DS,EAAW,KAAK,kBAAkBR,CAAO,EAE/C,OAAAK,EAAQ,YAAYC,CAAW,EAC/BD,EAAQ,YAAYE,CAAW,EAC/BF,EAAQ,YAAYG,CAAQ,EAE5BN,EAAO,YAAYE,CAAK,EACxBF,EAAO,YAAYG,CAAO,EAEnBH,CACT,CAKQ,mBACNC,EACAC,EACAV,EACmB,CACnB,IAAMC,EAAS,SAAS,cAAc,QAAQ,EAC9C,OAAAA,EAAO,UAAY,mBACnBA,EAAO,UAAYQ,EACnBR,EAAO,MAAQS,EACfT,EAAO,iBAAiB,QAASD,CAAO,EACjCC,CACT,CAKQ,kBAAkBD,EAAwC,CAChE,IAAMc,EAAW,SAAS,cAAc,QAAQ,EAIhD,GAHAA,EAAS,UAAY,mBACrBA,EAAS,MAAQ,eAEb,KAAK,OAAO,UAAW,CACzB,IAAMC,EAAM,SAAS,cAAc,KAAK,EACxCA,EAAI,IAAM,yBAAyB,KAAK,OAAO,SAAS,GACxDA,EAAI,IAAM,eACVA,EAAI,MAAM,MAAQ,OAClBA,EAAI,MAAM,OAAS,OACnBD,EAAS,YAAYC,CAAG,CAC1B,MACED,EAAS,UAAY,OAGvB,OAAAA,EAAS,iBAAiB,QAASd,CAAO,EACnCc,CACT,CAKA,YAAYP,EAA8B,CACxCA,EAAU,UAAY;AAAA;AAAA;AAAA;AAAA,KAKxB,CAKA,UAAUA,EAAwBS,EAAuB,CACvDT,EAAU,UAAY;AAAA;AAAA;AAAA,+CAGqBS,CAAO;AAAA;AAAA,KAGpD,CAKA,kBAAkBT,EAA8B,CAE9C,GAAI,CAAC,KAAK,OAAO,kBAAoB,CAAC,KAAK,OAAO,eAAgB,CAChE,KAAK,UAAUA,EAAW,4FAAiB,EAC3C,MACF,CAGA,IAAML,EAAUC,EAAU,KAAK,MAAM,EAG/Bc,EAAS,IAAI,gBAAgB,CACjC,MAAO,KAAK,OAAO,MACnB,iBAAkB,KAAK,OAAO,iBAC9B,eAAgB,KAAK,OAAO,eAC5B,MAAO,KAAK,OAAO,OAAS,kBAC5B,MAAO,KAAK,OAAO,OAAS,QAC5B,SAAU,MACZ,CAAC,EAEG,KAAK,OAAO,YACdA,EAAO,IAAI,aAAc,KAAK,OAAO,UAAU,EAE7C,KAAK,OAAO,WACdA,EAAO,IAAI,YAAa,KAAK,OAAO,SAAS,EAG/C,IAAMC,EAAU,GAAGhB,CAAO,kBAAkBe,EAAO,SAAS,CAAC,GAGvDE,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,IAAMD,EACbC,EAAO,UAAY,yBACnBA,EAAO,YAAc,IACrBA,EAAO,aAAa,oBAAqB,MAAM,EAG/CZ,EAAU,UAAY,GACtBA,EAAU,YAAYY,CAAM,CAC9B,CACF,ECnMO,IAAMC,EAAN,KAAqB,CAU1B,YAAYC,EAA+B,CAR3C,KAAQ,eAAqC,KAC7C,KAAQ,aAAsC,KAC9C,KAAQ,iBAA0C,KAClD,KAAQ,oBAA8B,EACtC,KAAQ,aAAuB,KAAK,IAAI,EACxC,KAAQ,eAAqC,KAC7C,KAAQ,WAAsB,GAiE9B,KAAQ,aAAoC,KA9D1C,KAAK,OAASA,CAChB,CAKA,kBAAkBC,EAA2B,CAC3C,KAAK,eAAiBA,CACxB,CAKA,YAAYC,EAAyB,CACnC,KAAK,WAAaA,CACpB,CAKA,sBAAsBC,EAAgC,CACpD,IAAMC,EAAS,CACb,YACA,YACA,WACA,SACA,aACA,OACF,EAEMC,EAAqB,IAAM,CAC/B,IAAMC,EAAM,KAAK,IAAI,EAGjBA,EAAM,KAAK,aAAe,MAI9B,KAAK,aAAeA,EACpB,KAAK,YAAY,EAIf,KAAK,OAAO,eACZ,KAAK,gBACL,CAAC,KAAK,YAEN,KAAK,kBAAkB,EAE3B,EAEAF,EAAO,QAASG,GAAU,CACxB,SAAS,iBAAiBA,EAAOF,EAAoB,CACnD,QAAS,GACT,QAAS,EACX,CAAC,CACH,CAAC,EAGD,KAAK,aAAeF,CACtB,CAOA,mBAA0B,CACxB,KAAK,aAAeK,EAAW,KAAK,YAAY,EAChD,KAAK,iBAAmBA,EAAW,KAAK,gBAAgB,EAGxD,IAAMF,EAAM,KAAK,IAAI,EACjBG,EAAQ,KAAK,OAAO,cAAgB,IAExC,GAAI,KAAK,oBAAsB,EAAG,CAChC,IAAMC,EAAgBJ,EAAM,KAAK,oBAC3BK,EAAc,KAAK,OAAO,oBAAsB,IAClDD,EAAgBC,IAClBF,EAAQE,EAAcD,EAE1B,CAEA,KAAK,aAAe,WAAW,IAAM,CAC/B,CAAC,KAAK,YAAc,KAAK,gBAC3B,KAAK,YAAY,CAErB,EAAGD,CAAK,CACV,CAKQ,aAAoB,CAC1B,GACE,GAAC,KAAK,OAAO,eACb,CAAC,KAAK,OAAO,aACb,KAAK,cAKF,KAAK,gBACR,KAAK,cAAc,EAGjB,KAAK,gBAAgB,CACvB,KAAK,eAAe,YAAc,KAAK,OAAO,YAC9C,KAAK,eAAe,UAAU,IAAI,SAAS,EAG3C,IAAMG,EAAW,KAAK,OAAO,iBAAmB,IAC5CA,EAAW,IACb,KAAK,iBAAmB,WAAW,IAAM,CACvC,KAAK,YAAY,CACnB,EAAGA,CAAQ,EAEf,CACF,CAKA,aAAoB,CACd,KAAK,gBACP,KAAK,eAAe,UAAU,OAAO,SAAS,EAEhD,KAAK,iBAAmBJ,EAAW,KAAK,gBAAgB,EACxD,KAAK,oBAAsB,KAAK,IAAI,CACtC,CAKQ,eAAsB,CAC5B,KAAK,eAAiB,SAAS,cAAc,KAAK,EAClD,KAAK,eAAe,UAAY,gBAChC,SAAS,KAAK,YAAY,KAAK,cAAc,EAG7C,IAAMK,EAAwB,IAAM,CAClC,GAAI,KAAK,gBAAkB,KAAK,eAAgB,CAC9C,IAAMC,EAAa,KAAK,eAAe,sBAAsB,EACvDC,EAAc,KAAK,eAAe,sBAAsB,EAGxDC,EAAgB,OAAO,YAAcF,EAAW,IAAM,GACtDG,EACJ,OAAO,WACPH,EAAW,KACXA,EAAW,MAAQ,EACnBC,EAAY,MAAQ,EAEtB,KAAK,eAAe,MAAM,OAASC,EAAgB,KACnD,KAAK,eAAe,MAAM,MAAQ,KAAK,IAAI,GAAIC,CAAY,EAAI,IACjE,CACF,EAGA,KAAK,eAAe,iBAAiB,QAAUC,GAAM,CACnDA,EAAE,gBAAgB,EACd,KAAK,cACP,KAAK,aAAa,CAEtB,CAAC,EAGD,OAAO,iBAAiB,SAAUL,CAAqB,EAGvD,sBAAsBA,CAAqB,CAC7C,CAKA,SAAgB,CACd,KAAK,aAAeL,EAAW,KAAK,YAAY,EAChD,KAAK,iBAAmBA,EAAW,KAAK,gBAAgB,EAEpD,KAAK,iBACP,KAAK,eAAe,OAAO,EAC3B,KAAK,eAAiB,MAGxB,KAAK,aAAe,KAAK,IAAI,EAC7B,KAAK,oBAAsB,CAC7B,CACF,EC1MO,SAASW,GAAqB,CACnC,IAAMC,EAAS,o0FAETC,EAAe,SAAS,cAAc,OAAO,EACnDA,EAAa,YAAcD,EAC3B,SAAS,KAAK,YAAYC,CAAY,CACxC,CCAO,IAAMC,EAAN,KAAoD,CAApD,cACL,aAAU,QAEV,KAAQ,OAAgC,CACtC,MAAO,GACP,iBAAkB,UAClB,eAAgB,UAChB,MAAO,kBACP,WAAY,GACZ,UAAW,GACX,OAAQ,GACR,MAAO,QACP,cAAe,GACf,YAAa,qEACb,aAAc,IACd,gBAAiB,IACjB,mBAAoB,GACtB,EAMA,KAAQ,eAA2C,KACnD,KAAQ,cAAuC,KAC/C,KAAQ,WAAsB,GAC9B,KAAQ,YAAuB,GAC/B,KAAQ,YAAuB,GAC/B,KAAQ,YAAuB,GAK/B,MAAM,KAAKC,EAA+C,CACxD,GAAI,CAKF,GAHA,KAAK,OAAS,CAAE,GAAG,KAAK,OAAQ,GAAGA,CAAQ,EAGvC,CAAC,KAAK,OAAO,MAAO,CACtB,QAAQ,MAAM,oCAAoC,EAClD,MACF,CASA,GANA,KAAK,kBAAoB,IAAIC,EAAkB,KAAK,MAAM,EAC1D,KAAK,aAAe,IAAIC,EAAa,KAAK,MAAM,EAChD,KAAK,eAAiB,IAAIC,EAAe,KAAK,MAAM,EAIhD,CADY,MAAM,KAAK,kBAAkB,wBAAwB,EAEnE,OAIF,KAAK,YAAc,GAGf,SAAS,aAAe,UAC1B,SAAS,iBAAiB,mBAAoB,IAAM,CAClD,WAAW,IAAM,KAAK,WAAW,EAAG,GAAG,CACzC,CAAC,EAED,WAAW,IAAM,KAAK,WAAW,EAAG,GAAG,CAE3C,OAASC,EAAO,CACd,QAAQ,MAAM,yCAA0CA,CAAK,EACzD,OAAO,KAAK,OAAO,SAAY,YACjC,KAAK,OAAO,QACVA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CACvD,CAEJ,CACF,CAKQ,YAAmB,CACzB,GAAI,CAEFC,EAAa,EAGb,KAAK,eAAiB,KAAK,aAAa,qBAAqB,IAC3D,KAAK,OAAO,CACd,EACA,SAAS,KAAK,YAAY,KAAK,cAAc,EAG7C,KAAK,eAAe,kBAAkB,KAAK,cAAc,EACzD,KAAK,eAAe,sBAAsB,IAAM,KAAK,OAAO,CAAC,EAGzD,KAAK,OAAO,eACd,KAAK,eAAe,kBAAkB,EAGxC,QAAQ,IAAI,0CAA0C,CACxD,OAASD,EAAO,CACd,QAAQ,MAAM,yCAA0CA,CAAK,EACzD,OAAO,KAAK,OAAO,SAAY,YACjC,KAAK,OAAO,QACVA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CACvD,CAEJ,CACF,CAKA,MAAM,MAAsB,CAE1B,GAAI,CAAC,KAAK,YAAa,CAErB,GAAI,CADY,MAAM,KAAK,kBAAkB,wBAAwB,EAEnE,OAEF,KAAK,YAAc,EACrB,CAKA,GAFA,KAAK,eAAe,YAAY,EAE5B,CAAC,KAAK,cAAe,CACvB,KAAK,cAAgB,KAAK,aAAa,oBACrC,IAAM,KAAK,aAAa,EACxB,IAAM,KAAK,eAAe,EAC1B,IAAM,KAAK,MAAM,CACnB,EACA,SAAS,KAAK,YAAY,KAAK,aAAa,EAE5C,IAAME,EAAiB,KAAK,cAAc,cACxC,qBACF,EACIA,GACF,KAAK,aAAa,kBAAkBA,CAA6B,CAErE,CAEA,KAAK,cAAc,UAAU,IAAI,SAAS,EACtC,KAAK,iBACP,KAAK,eAAe,MAAM,QAAU,QAEtC,KAAK,WAAa,GAClB,KAAK,YAAc,GACnB,KAAK,eAAe,YAAY,EAAI,CACtC,CAKA,OAAc,CACR,KAAK,gBACP,KAAK,cAAc,UAAU,OAAO,SAAS,EAC7C,KAAK,cAAc,UAAU,OAAO,WAAW,EAC/C,KAAK,cAAc,UAAU,OAAO,WAAW,GAE7C,KAAK,iBACP,KAAK,eAAe,MAAM,QAAU,QAEtC,KAAK,WAAa,GAClB,KAAK,YAAc,GACnB,KAAK,YAAc,GACnB,KAAK,eAAe,YAAY,EAAK,EAGjC,KAAK,OAAO,eACd,KAAK,eAAe,kBAAkB,CAE1C,CAKA,MAAM,QAAwB,CACxB,KAAK,WACP,KAAK,MAAM,EAEX,MAAM,KAAK,KAAK,CAEpB,CAKQ,cAAqB,CACvB,KAAK,gBACP,KAAK,cAAc,UAAU,IAAI,WAAW,EAC5C,KAAK,YAAc,GAEvB,CAKQ,gBAAuB,CACzB,KAAK,gBACH,KAAK,aACP,KAAK,cAAc,UAAU,OAAO,WAAW,EAC/C,KAAK,YAAc,KAEnB,KAAK,cAAc,UAAU,IAAI,WAAW,EAC5C,KAAK,YAAc,IAGzB,CAKA,SAAgB,CAEV,KAAK,iBACP,KAAK,eAAe,OAAO,EAC3B,KAAK,eAAiB,MAEpB,KAAK,gBACP,KAAK,cAAc,OAAO,EAC1B,KAAK,cAAgB,MAIvB,KAAK,gBAAgB,QAAQ,EAG7B,KAAK,WAAa,GAClB,KAAK,YAAc,GACnB,KAAK,YAAc,GACnB,KAAK,YAAc,EACrB,CACF,ECzNA,IAAMC,EAAa,IAAIC,EAGvB,OAAO,gBAAkB,CACvB,KAAMD,EAAW,KAAK,KAAKA,CAAU,EACrC,QAASA,EAAW,QAAQ,KAAKA,CAAU,EAC3C,KAAMA,EAAW,KAAK,KAAKA,CAAU,EACrC,MAAOA,EAAW,MAAM,KAAKA,CAAU,EACvC,OAAQA,EAAW,OAAO,KAAKA,CAAU,EACzC,QAASA,EAAW,OACtB", "names": ["getApiUrl", "config", "scriptElement", "scriptSrc", "clearTimer", "timer", "ValidationService", "config", "apiUrl", "getApiUrl", "currentDomain", "response", "data", "error", "validation", "UIComponents", "config", "onClick", "button", "baseUrl", "getApiUrl", "onMinimize", "onMaximize", "onClose", "container", "header", "content", "title", "actions", "minimizeBtn", "maximizeBtn", "closeBtn", "img", "message", "params", "chatUrl", "iframe", "TooltipManager", "config", "button", "expanded", "onToggleChat", "events", "updateUserActivity", "now", "event", "clearTimer", "delay", "timeSinceHide", "repeatDelay", "duration", "updateTooltipPosition", "buttonRect", "tooltipRect", "tooltipBottom", "tooltipRight", "e", "createStyles", "styles", "styleElement", "KoalaChatWidget", "options", "ValidationService", "UIComponents", "TooltipManager", "error", "createStyles", "contentElement", "chatWidget", "KoalaChatWidget"]}