body{
  margin: 0;
  padding: 0;
}

.adminLayout {
  display: flex;
  height: 100vh;
  background-color: #f7f9fc;
}

.sidebarContainer {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  background-color: white;
  transition: all 0.3s;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 20;
}

.sidebarOpen {
  width: 16rem; /* 64px */
}

.sidebarClosed {
  width: 5rem; /* 20px */
}

.sidebarLogo {
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.mainContent {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.headerContainer {
  background-color: white;
  height: 4rem;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.contentContainer {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
}

.contentWrapper {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  min-height: calc(100vh - 10rem);
}

.navItem {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.navItemActive {
  background-color: #f0f7ff;
  color: #0771c9;
  font-weight: 500;
}

.navItemIcon {
  font-size: 1.25rem;
}

.navItemLabel {
  margin-left: 0.75rem;
}

@media (min-width: 768px) {
  .sidebarContainer {
    position: relative;
  }
} 